import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

export const getServersList = () => {
    return axios.get(`${API_BASE_URL}/servers`);
};

export const getServerStatus = (serverId: number) => {
    return axios.get(`${API_BASE_URL}/servers/${serverId}/status`);
};

export const executeCommand = (id: number, command: string) => {
    return axios.post(`${API_BASE_URL}/servers/${id}/execute`, { command });
};

export const getServerCrons = (serverId: number) => {
    return axios.get(`${API_BASE_URL}/servers/${serverId}/crons`);
};

export const addServerCron = (serverId: number, schedule: string, command: string) => {
    return axios.post(`${API_BASE_URL}/servers/${serverId}/crons`, { schedule, command });
};

export const deleteServerCron = (serverId: number, cronLine: string) => {
    return axios.delete(`${API_BASE_URL}/servers/${serverId}/crons`, {
        data: { cron_line: cronLine }
    });
};

export const listServerFiles = (serverId: number, path: string = '/') => {
    return axios.get(`${API_BASE_URL}/servers/${serverId}/files`, {
        params: { path }
    });
};

export const readServerFile = (serverId: number, path: string) => {
    return axios.get(`${API_BASE_URL}/servers/${serverId}/files/read`, {
        params: { path }
    });
};

export const writeServerFile = (serverId: number, path: string, content: string) => {
    return axios.post(`${API_BASE_URL}/servers/${serverId}/files/write`, {
        file_path: path,
        content: content
    });
};

export const createServerFile = (serverId: number, path: string) => {
    return axios.post(`${API_BASE_URL}/servers/${serverId}/files/create`, {
        file_path: path
    });
};

export const createServerDirectory = (serverId: number, path: string) => {
    return axios.post(`${API_BASE_URL}/servers/${serverId}/directories/create`, {
        directory_path: path
    });
};

export const openFileInEditor = (serverId: number, path: string, editor: 'nano' | 'vi' = 'nano') => {
    return axios.post(`${API_BASE_URL}/servers/${serverId}/editor`, {
        path,
        editor
    });
}; 