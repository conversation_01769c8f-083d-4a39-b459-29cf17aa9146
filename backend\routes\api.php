<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ServerController; // Vérifie que l'import est bien là
use Illuminate\Http\Request;

Route::get('/servers', [ServerController::class, 'index']);
Route::get('/servers/{id}/status', [ServerController::class, 'status']);
Route::post('/servers/{id}/execute', [ServerController::class, 'executeCommand']);
Route::get('/servers/{id}/crons', [ServerController::class, 'getCrons']);
Route::post('/servers/{id}/crons', [ServerController::class, 'addCron']);
Route::delete('/servers/{id}/crons', [ServerController::class, 'deleteCron']);
Route::get('/servers/{id}/files', [ServerController::class, 'listFiles']);
Route::get('/servers/{id}/files/read', [ServerController::class, 'readFile']);
Route::post('/servers/{id}/files/write', [ServerController::class, 'writeFile']);
Route::post('/servers/{id}/files/create', [ServerController::class, 'createFile']);
Route::post('/servers/{id}/directories/create', [ServerController::class, 'createDirectory']);
Route::post('/servers/{id}/editor', [ServerController::class, 'openEditor']);
?>