import { useEffect, useState } from "react";
import { getServer<PERSON>tatus, getServers<PERSON><PERSON>, execute<PERSON>ommand, getServerCrons, addServerCron, deleteServerCron, listServerFiles, readServerFile, writeServerFile, createServerFile, createServerDirectory, openFileInEditor } from "../api";
import { FaServer, FaMicrochip, FaGlobe, FaMemory, FaChartLine, FaHdd, FaNetworkWired, FaTerminal, FaClock, FaFolder, FaFile, FaEdit, FaSave, FaArrowLeft, FaPlus } from "react-icons/fa";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface Server {
    id: number;
    name: string;
    url: string;
}

interface ServerStatus {
    server: Server;
    cpu: string;
    ram: string;
    disks: { [key: string]: string };
    system: {
        os: string;
        uptime: string;
        load_average: string;
        processes: {
            total: string;
            running: string;
            sleeping: string;
            zombie: string;
        };
        cpu_info: {
            model: string;
            cores: string;
            threads: string;
        };
    };
    network: {
        interfaces: {
            [key: string]: {
                rx: string;
                tx: string;
            };
        };
        connections: {
            total: string;
            listening: string;
            waiting: string;
        };
        open_ports: string[];
    };
}

interface DataPoint {
    time: string;
    value: number;
}

interface CronData {
    user_crons: string;
    system_crons: string;
}

interface FileInfo {
    name: string;
    type: 'file' | 'directory';
    permissions: string;
    owner: string;
    group: string;
    size: string;
    date: string;
}

interface EditorResponse {
    success: boolean;
    message: string;
    command: string;
    requires_sudo: boolean;
    requires_password?: boolean;
    editor: string;
    error?: string;
}

const Dashboard = () => {
    const [servers, setServers] = useState<Server[]>([]);
    const [selectedServerId, setSelectedServerId] = useState<number | null>(null);
    const [serverStatus, setServerStatus] = useState<ServerStatus | null>(null);
    const [historicalData, setHistoricalData] = useState<{
        cpu: DataPoint[];
        ram: DataPoint[];
        disks: { [key: string]: DataPoint[] };
    }>({
        cpu: [],
        ram: [],
        disks: {}
    });
    const [command, setCommand] = useState("");
    const [commandOutput, setCommandOutput] = useState("");
    const [isExecuting, setIsExecuting] = useState(false);
    const [cronData, setCronData] = useState<CronData | null>(null);
    const [newCronSchedule, setNewCronSchedule] = useState("");
    const [newCronCommand, setNewCronCommand] = useState("");
    const [cronError, setCronError] = useState("");
    const [currentPath, setCurrentPath] = useState('/');
    const [files, setFiles] = useState<FileInfo[]>([]);
    const [selectedFile, setSelectedFile] = useState<string | null>(null);
    const [fileContent, setFileContent] = useState('');
    const [isEditing, setIsEditing] = useState(false);
    const [fileError, setFileError] = useState('');
    const [selectedServer, setSelectedServer] = useState<Server | null>(null);
    const [commandToExecute, setCommandToExecute] = useState('');
    const [error, setError] = useState('');
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [newItemName, setNewItemName] = useState('');
    const [newItemType, setNewItemType] = useState<'file' | 'directory'>('file');
    const [isFileManagerCollapsed, setIsFileManagerCollapsed] = useState(false);
    const [showApacheConfigModal, setShowApacheConfigModal] = useState(false);
    const [apacheConfig, setApacheConfig] = useState({
        domain: '',
        username: '',
        customDocRoot: '',
        enableSSL: false,
        serverAdmin: 'webmaster@localhost',
        useSystemctl: true
    });

    useEffect(() => {
        getServersList().then(response => {
            setServers(response.data);
            if (response.data.length > 0) {
                setSelectedServerId(response.data[0].id);
            }
        }).catch(error => {
            console.error("Erreur lors de la récupération des serveurs:", error);
        });
    }, []);

    useEffect(() => {
        if (selectedServerId) {
            const fetchData = () => {
                getServerStatus(selectedServerId).then(response => {
                    setServerStatus(response.data);
                    const now = new Date().toLocaleTimeString();

                    // Mise à jour des données historiques pour les disques
                    const newDisksData: { [key: string]: DataPoint[] } = {};
                    Object.entries(response.data.disks).forEach(([disk, usage]) => {
                        if (typeof usage === 'string') {
                            const usageValue = parseFloat(usage.replace('%', ''));
                            newDisksData[disk] = [
                                ...(historicalData.disks[disk]?.slice(-9) || []),
                                { time: now, value: usageValue }
                            ];
                        }
                    });

                    setHistoricalData(prev => ({
                        cpu: [...prev.cpu.slice(-9), { time: now, value: parseFloat(response.data.cpu) }],
                        ram: [...prev.ram.slice(-9), { time: now, value: parseFloat(response.data.ram) }],
                        disks: newDisksData
                    }));
                }).catch(error => {
                    console.error("Erreur lors de la récupération du statut du serveur:", error);
                });
            };

            fetchData();
            const interval = setInterval(fetchData, 5000);
            return () => clearInterval(interval);
        }
    }, [selectedServerId]);

    useEffect(() => {
        if (selectedServerId) {
            fetchCrons();
        }
    }, [selectedServerId]);

    useEffect(() => {
        if (selectedServerId) {
            fetchFiles();
        }
    }, [selectedServerId, currentPath]);

    const fetchCrons = async () => {
        if (!selectedServerId) return;
        try {
            const response = await getServerCrons(selectedServerId);
            setCronData(response.data);
        } catch (error: any) {
            console.error("Erreur lors de la récupération des crons:", error);
        }
    };

    const fetchFiles = async () => {
        if (!selectedServerId) return;
        try {
            setFileError('');
            const response = await listServerFiles(selectedServerId, currentPath);
            setFiles(response.data.files);
        } catch (error: any) {
            setFileError(error.response?.data?.error || "Erreur lors de la récupération des fichiers");
        }
    };

    const handleExecuteCommand = async () => {
        if (!selectedServerId || !command.trim()) return;

        setIsExecuting(true);
        try {
            const response = await executeCommand(selectedServerId, command.trim());
            setCommandOutput(response.data.output);
        } catch (error: any) {
            console.error("Erreur lors de l'exécution de la commande:", error);
            setCommandOutput("Erreur: " + (error.response?.data?.error || error.message));
        }
        setIsExecuting(false);
    };

    const handleAddCron = async () => {
        if (!selectedServerId) return;
        try {
            setCronError("");
            await addServerCron(selectedServerId, newCronSchedule, newCronCommand);
            await fetchCrons();
            setNewCronSchedule("");
            setNewCronCommand("");
        } catch (error: any) {
            setCronError(error.response?.data?.error || "Erreur lors de l'ajout du cron");
        }
    };

    const handleDeleteCron = async (cronLine: string) => {
        if (!selectedServerId) return;
        try {
            await deleteServerCron(selectedServerId, cronLine);
            await fetchCrons();
        } catch (error: any) {
            console.error("Erreur lors de la suppression du cron:", error);
        }
    };

    const handleFileClick = async (file: FileInfo) => {
        if (file.type === 'directory') {
            const newPath = currentPath === '/'
                ? `/${file.name}`
                : `${currentPath}/${file.name}`;
            setCurrentPath(newPath);
        } else {
            try {
                setFileError('');
                const response = await readServerFile(selectedServerId!, `${currentPath}/${file.name}`);
                setSelectedFile(file.name);
                setFileContent(response.data.content);
                setIsEditing(true);
            } catch (error: any) {
                setFileError(error.response?.data?.error || "Erreur lors de la lecture du fichier");
            }
        }
    };

    const handleSaveFile = async () => {
        if (!selectedServerId || !selectedFile) return;
        try {
            setFileError('');
            const filePath = currentPath === '/' ? `/${selectedFile}` : `${currentPath}/${selectedFile}`;

            // Afficher un message de sauvegarde en cours
            setCommandOutput("Sauvegarde en cours...");

            // Afficher les paramètres envoyés pour le debug
            console.log("Paramètres envoyés:", {
                serverId: selectedServerId,
                file_path: filePath,
                content: fileContent
            });

            const response = await writeServerFile(selectedServerId, filePath, fileContent);

            // Afficher les messages de debug dans la console
            console.log("Réponse de la sauvegarde:", response.data);

            if (response.data.success) {
                setCommandOutput(prevOutput => prevOutput + "\nFichier sauvegardé avec succès!");

                // Rafraîchir le contenu
                const readResponse = await readServerFile(selectedServerId, filePath);
                setFileContent(readResponse.data.content);
                setIsEditing(false);
            } else {
                const errorMsg = response.data.error || "Erreur inconnue";
                const details = response.data.details ? `\nDétails: ${response.data.details}` : '';
                setFileError(`Erreur lors de la sauvegarde: ${errorMsg}${details}`);
                setCommandOutput(prevOutput => prevOutput + `\nErreur lors de la sauvegarde: ${errorMsg}${details}`);
            }
        } catch (error: any) {
            console.error("Erreur complète:", error);
            const errorMessage = error.response?.data?.error || "Erreur lors de la sauvegarde du fichier";
            const errorDetails = error.response?.data?.details || error.message;
            const fullError = `${errorMessage}\n${errorDetails}`;

            console.error("Erreur détaillée:", error.response?.data);
            setFileError(fullError);
            setCommandOutput(prevOutput => prevOutput + `\nErreur: ${fullError}`);
        }
    };

    const handleOpenInEditor = async (editor: 'nano' | 'vi') => {
        if (!selectedServer) {
            setError("Veuillez sélectionner un serveur");
            return;
        }

        if (!selectedFile) {
            setError("Veuillez sélectionner un fichier");
            return;
        }

        const filePath = currentPath + '/' + selectedFile;

        try {
            const response = await openFileInEditor(selectedServer.id, filePath, editor);
            const data = response.data as EditorResponse;

            if (data.success) {
                if (data.requires_sudo) {
                    if (data.requires_password) {
                        // Pour les commandes sudo avec mot de passe
                        setCommandToExecute(data.command);
                        setCommandOutput("Commande d'édition avec sudo (mot de passe requis)...");
                    } else {
                        // Pour les commandes sudo sans mot de passe
                        setCommandToExecute(data.command);
                        setCommandOutput("Commande d'édition avec sudo...");
                    }
                } else {
                    // Pour les commandes sans sudo
                    setCommandToExecute(data.command);
                    setCommandOutput("Commande d'édition standard...");
                }
            } else {
                setError(data.error || "Erreur lors de l'ouverture de l'éditeur");
            }
        } catch (error: any) {
            setError(error.response?.data?.error || "Erreur lors de l'ouverture de l'éditeur");
        }
    };

    const navigateUp = () => {
        if (currentPath === '/') return;
        const newPath = currentPath.split('/').slice(0, -1).join('/') || '/';
        setCurrentPath(newPath);
    };

    const handleDownloadFile = async (file: FileInfo) => {
        if (!selectedServerId) return;
        try {
            setFileError('');
            const response = await readServerFile(selectedServerId, `${currentPath}/${file.name}`);

            // Créer un blob avec le contenu du fichier
            const blob = new Blob([response.data.content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);

            // Créer un lien de téléchargement temporaire
            const a = document.createElement('a');
            a.href = url;
            a.download = file.name;
            document.body.appendChild(a);
            a.click();

            // Nettoyer
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error: any) {
            setFileError(error.response?.data?.error || "Erreur lors du téléchargement du fichier");
        }
    };

    const handleCreateItem = async () => {
        if (!selectedServerId || !newItemName) return;
        try {
            setFileError('');
            const newPath = currentPath === '/' ? `/${newItemName}` : `${currentPath}/${newItemName}`;

            if (newItemType === 'file') {
                await createServerFile(selectedServerId, newPath);
            } else {
                await createServerDirectory(selectedServerId, newPath);
            }

            // Rafraîchir la liste des fichiers
            await fetchFiles();
            setShowCreateModal(false);
            setNewItemName('');
        } catch (error: any) {
            setFileError(error.response?.data?.error || `Erreur lors de la création ${newItemType === 'file' ? 'du fichier' : 'du dossier'}`);
        }
    };

    const handleCreateApacheConfig = async () => {
        if (!selectedServerId || !apacheConfig.domain || !apacheConfig.username) return;
        try {
            setFileError('');
            const configContent = `<VirtualHost *:80>
    ServerAdmin ${apacheConfig.serverAdmin}
    ServerName ${apacheConfig.domain}
    ServerAlias www.${apacheConfig.domain}
    DocumentRoot ${apacheConfig.customDocRoot || `/home/<USER>/www/${apacheConfig.domain}`}
    
    <Directory ${apacheConfig.customDocRoot || `/home/<USER>/www/${apacheConfig.domain}`}>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog logs/${apacheConfig.domain}-error.log
    CustomLog logs/${apacheConfig.domain}-access.log combined
</VirtualHost>

${apacheConfig.enableSSL ? `<VirtualHost *:443>
    ServerAdmin ${apacheConfig.serverAdmin}
    ServerName ${apacheConfig.domain}
    ServerAlias www.${apacheConfig.domain}
    DocumentRoot ${apacheConfig.customDocRoot || `/home/<USER>/www/${apacheConfig.domain}`}
    
    <Directory ${apacheConfig.customDocRoot || `/home/<USER>/www/${apacheConfig.domain}`}>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog logs/${apacheConfig.domain}-error.log
    CustomLog logs/${apacheConfig.domain}-access.log combined

    SSLEngine on
    SSLCertificateFile /etc/pki/tls/certs/${apacheConfig.domain}.crt
    SSLCertificateKeyFile /etc/pki/tls/private/${apacheConfig.domain}.key
</VirtualHost>` : ''}`;

            // Vérifier si le fichier de configuration principal existe
            const checkMainConf = await executeCommand(selectedServerId, 'test -f /etc/httpd/conf/httpd.conf && echo "main" || echo "user"');
            const configPath = checkMainConf.data.output.trim() === 'main'
                ? '/etc/httpd/conf/httpd.conf'
                : `/etc/httpd/conf.d/${apacheConfig.username}.conf`;

            // Créer le fichier de configuration
            await createServerFile(selectedServerId, configPath);
            await writeServerFile(selectedServerId, configPath, configContent);

            // Créer le répertoire du site avec les bonnes permissions
            const docRoot = apacheConfig.customDocRoot || `/home/<USER>/www/${apacheConfig.domain}`;
            await executeCommand(selectedServerId, `mkdir -p ${docRoot}`);
            await executeCommand(selectedServerId, `chown -R ${apacheConfig.username}:${apacheConfig.username} ${docRoot}`);
            await executeCommand(selectedServerId, `chmod -R 755 ${docRoot}`);

            // Tester la configuration
            await executeCommand(selectedServerId, 'httpd -t');

            // Redémarrer le service selon le type de système
            if (apacheConfig.useSystemctl) {
                await executeCommand(selectedServerId, 'systemctl reload httpd');
            } else {
                await executeCommand(selectedServerId, 'service httpd reload');
            }

            setShowApacheConfigModal(false);
            setCommandOutput("Configuration du VirtualHost créée avec succès !");

            // Rafraîchir la liste des fichiers
            await fetchFiles();
        } catch (error: any) {
            setFileError(error.response?.data?.error || "Erreur lors de la création de la configuration du VirtualHost");
            setCommandOutput(prevOutput => prevOutput + "\nErreur: " + (error.response?.data?.error || error.message));
        }
    };

    return (
        <div className="min-h-screen bg-gray-100 p-6 flex flex-col items-center">
            <div className="w-full max-w-5xl bg-white shadow-md rounded-xl p-6">
                <h1 className="text-3xl font-bold flex items-center gap-2 text-gray-800">
                    <FaServer className="text-blue-500" /> État des serveurs
                </h1>
                <p className="text-gray-500">Surveillez l'état de vos serveurs en temps réel</p>
                <div className="mt-4">
                    <label className="text-lg font-medium text-gray-700">Sélectionner un serveur :</label>
                    <select
                        className="w-full p-3 mt-2 bg-gray-50 border border-gray-300 rounded-lg focus:ring focus:ring-blue-300"
                        value={selectedServerId || ""}
                        onChange={(e) => setSelectedServerId(Number(e.target.value))}
                    >
                        {servers.map(server => (
                            <option key={server.id} value={server.id}>{server.name}</option>
                        ))}
                    </select>
                </div>
            </div>

            {serverStatus && (
                <>
                    {/* Informations système */}
                    <div className="w-full max-w-5xl mt-6 bg-white shadow-md rounded-xl p-6">
                        <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                            <FaServer className="text-blue-500" /> Informations système
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 className="text-lg font-medium text-gray-700 mb-4">Système</h3>
                                <div className="space-y-2">
                                    <p><span className="font-medium">OS:</span> {serverStatus.system.os}</p>
                                    <p><span className="font-medium">Uptime:</span> {serverStatus.system.uptime}</p>
                                    <p><span className="font-medium">Load Average:</span> {serverStatus.system.load_average}</p>
                                </div>
                            </div>
                            <div>
                                <h3 className="text-lg font-medium text-gray-700 mb-4">CPU</h3>
                                <div className="space-y-2">
                                    <p><span className="font-medium">Modèle:</span> {serverStatus.system.cpu_info.model}</p>
                                    <p><span className="font-medium">Cœurs:</span> {serverStatus.system.cpu_info.cores}</p>
                                    <p><span className="font-medium">Threads:</span> {serverStatus.system.cpu_info.threads}</p>
                                </div>
                            </div>
                            <div>
                                <h3 className="text-lg font-medium text-gray-700 mb-4">Processus</h3>
                                <div className="space-y-2">
                                    <p><span className="font-medium">Total:</span> {serverStatus.system.processes.total}</p>
                                    <p><span className="font-medium">En cours:</span> {serverStatus.system.processes.running}</p>
                                    <p><span className="font-medium">En attente:</span> {serverStatus.system.processes.sleeping}</p>
                                    <p><span className="font-medium">Zombies:</span> {serverStatus.system.processes.zombie}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Informations réseau */}
                    <div className="w-full max-w-5xl mt-6 bg-white shadow-md rounded-xl p-6">
                        <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                            <FaNetworkWired className="text-blue-500" /> État du réseau
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 className="text-lg font-medium text-gray-700 mb-4">Connexions</h3>
                                <div className="space-y-2">
                                    <p><span className="font-medium">Total:</span> {serverStatus.network.connections.total}</p>
                                    <p><span className="font-medium">En écoute:</span> {serverStatus.network.connections.listening}</p>
                                    <p><span className="font-medium">En attente:</span> {serverStatus.network.connections.waiting}</p>
                                </div>
                            </div>
                            <div>
                                <h3 className="text-lg font-medium text-gray-700 mb-4">Ports ouverts</h3>
                                <div className="flex flex-wrap gap-2">
                                    {serverStatus.network.open_ports.map(port => (
                                        <span key={port} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">
                                            {port}
                                        </span>
                                    ))}
                                </div>
                            </div>
                            <div className="md:col-span-2">
                                <h3 className="text-lg font-medium text-gray-700 mb-4">Interfaces réseau</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {Object.entries(serverStatus.network.interfaces).map(([iface, stats]) => (
                                        <div key={iface} className="bg-gray-50 p-4 rounded-lg">
                                            <h4 className="font-medium text-gray-700 mb-2">{iface}</h4>
                                            <div className="space-y-1 text-sm">
                                                <p><span className="font-medium">Reçu:</span> {(parseInt(stats.rx) / 1024 / 1024).toFixed(2)} MB</p>
                                                <p><span className="font-medium">Transmis:</span> {(parseInt(stats.tx) / 1024 / 1024).toFixed(2)} MB</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="w-full max-w-5xl mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="bg-white shadow-md rounded-xl p-6 flex flex-col items-center text-center">
                            <FaGlobe className="text-blue-500 text-3xl mb-3" />
                            <h2 className="text-lg font-bold text-gray-800">{serverStatus.server.name}</h2>
                            <p className="text-sm text-green-500 font-semibold">● En ligne</p>
                        </div>

                        <div className="bg-white shadow-md rounded-xl p-6 flex flex-col items-center text-center">
                            <FaMicrochip className="text-yellow-500 text-3xl mb-3" />
                            <h3 className="text-lg font-medium text-gray-700">Utilisation CPU</h3>
                            <p className="text-2xl font-bold text-gray-900">{serverStatus.cpu}%</p>
                        </div>

                        <div className="bg-white shadow-md rounded-xl p-6 flex flex-col items-center text-center">
                            <FaMemory className="text-green-500 text-3xl mb-3" />
                            <h3 className="text-lg font-medium text-gray-700">Utilisation RAM</h3>
                            <p className="text-2xl font-bold text-gray-900">{serverStatus.ram}</p>
                        </div>

                        {/* Disques */}
                        {Object.entries(serverStatus.disks).map(([disk, usage]) => (
                            <div key={disk} className="bg-white shadow-md rounded-xl p-6 flex flex-col items-center text-center">
                                <FaHdd className="text-red-500 text-3xl mb-3" />
                                <h3 className="text-lg font-medium text-gray-700">Disque {disk}</h3>
                                <p className="text-2xl font-bold text-gray-900">{usage}</p>
                            </div>
                        ))}
                    </div>

                    <div className="w-full max-w-5xl mt-6 bg-white shadow-md rounded-xl p-6">
                        <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                            <FaChartLine className="text-blue-500" /> Statistiques en temps réel
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {/* CPU Graph */}
                            <div className="h-64">
                                <h3 className="text-lg font-medium text-gray-700 mb-2">CPU</h3>
                                <ResponsiveContainer width="100%" height="100%">
                                    <LineChart data={historicalData.cpu}>
                                        <CartesianGrid strokeDasharray="3 3" />
                                        <XAxis dataKey="time" />
                                        <YAxis domain={[0, 100]} />
                                        <Tooltip />
                                        <Line type="monotone" dataKey="value" stroke="#fbbf24" strokeWidth={2} />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>

                            {/* RAM Graph */}
                            <div className="h-64">
                                <h3 className="text-lg font-medium text-gray-700 mb-2">RAM</h3>
                                <ResponsiveContainer width="100%" height="100%">
                                    <LineChart data={historicalData.ram}>
                                        <CartesianGrid strokeDasharray="3 3" />
                                        <XAxis dataKey="time" />
                                        <YAxis domain={[0, 100]} />
                                        <Tooltip />
                                        <Line type="monotone" dataKey="value" stroke="#10b981" strokeWidth={2} />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>

                            {/* Disk Graphs */}
                            {Object.entries(historicalData.disks).map(([disk, data]) => (
                                <div key={disk} className="h-64">
                                    <h3 className="text-lg font-medium text-gray-700 mb-2">Disque {disk}</h3>
                                    <ResponsiveContainer width="100%" height="100%">
                                        <LineChart data={data}>
                                            <CartesianGrid strokeDasharray="3 3" />
                                            <XAxis dataKey="time" />
                                            <YAxis domain={[0, 100]} />
                                            <Tooltip />
                                            <Line type="monotone" dataKey="value" stroke="#ef4444" strokeWidth={2} />
                                        </LineChart>
                                    </ResponsiveContainer>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Terminal et gestion des paquets */}
                    <div className="w-full max-w-5xl mt-6 bg-white shadow-md rounded-xl p-6">
                        <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                            <FaTerminal className="text-blue-500" /> Terminal
                        </h2>
                        <div className="space-y-4">
                            <div className="flex gap-2">
                                <input
                                    type="text"
                                    value={command}
                                    onChange={(e) => setCommand(e.target.value)}
                                    placeholder="Entrez une commande (ex: apt-get update)"
                                    className="flex-1 p-2 border border-gray-300 rounded-lg"
                                    onKeyPress={(e) => e.key === 'Enter' && handleExecuteCommand()}
                                />
                                <button
                                    onClick={handleExecuteCommand}
                                    disabled={isExecuting}
                                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
                                >
                                    {isExecuting ? 'Exécution...' : 'Exécuter'}
                                </button>
                            </div>
                            {commandOutput && (
                                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto whitespace-pre-wrap">
                                    {commandOutput}
                                </pre>
                            )}
                            <div className="mt-4">
                                <h3 className="text-lg font-medium text-gray-700 mb-2">Commandes rapides</h3>
                                <div className="flex flex-wrap gap-2">
                                    <button
                                        onClick={() => setCommand('apt-get update')}
                                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                                    >
                                        Mettre à jour les paquets
                                    </button>
                                    <button
                                        onClick={() => setCommand('apt-get upgrade')}
                                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                                    >
                                        Mettre à niveau les paquets
                                    </button>
                                    <button
                                        onClick={() => setCommand('systemctl status')}
                                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                                    >
                                        État des services
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Gestion des Crons */}
                    <div className="w-full max-w-5xl mt-6 bg-white shadow-md rounded-xl p-6">
                        <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                            <FaClock className="text-blue-500" /> Tâches planifiées (Crons)
                        </h2>
                        <div className="space-y-6">
                            {/* Formulaire d'ajout de cron */}
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium text-gray-700">Ajouter une nouvelle tâche</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <input
                                        type="text"
                                        value={newCronSchedule}
                                        onChange={(e) => setNewCronSchedule(e.target.value)}
                                        placeholder="Planification (ex: * * * * *)"
                                        className="p-2 border border-gray-300 rounded-lg"
                                    />
                                    <input
                                        type="text"
                                        value={newCronCommand}
                                        onChange={(e) => setNewCronCommand(e.target.value)}
                                        placeholder="Commande"
                                        className="p-2 border border-gray-300 rounded-lg"
                                    />
                                </div>
                                {cronError && (
                                    <p className="text-red-500 text-sm">{cronError}</p>
                                )}
                                <button
                                    onClick={handleAddCron}
                                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                                >
                                    Ajouter
                                </button>
                            </div>

                            {/* Affichage des crons */}
                            {cronData && (
                                <div className="space-y-6">
                                    <div>
                                        <h3 className="text-lg font-medium text-gray-700 mb-2">Crons utilisateur</h3>
                                        <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto whitespace-pre-wrap">
                                            {cronData.user_crons}
                                        </pre>
                                    </div>
                                    <div>
                                        <h3 className="text-lg font-medium text-gray-700 mb-2">Crons système</h3>
                                        <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto whitespace-pre-wrap">
                                            {cronData.system_crons}
                                        </pre>
                                    </div>
                                </div>
                            )}

                            {/* Guide d'utilisation */}
                            <div className="mt-4 bg-blue-50 p-4 rounded-lg">
                                <h4 className="font-medium text-blue-800 mb-2">Guide de planification</h4>
                                <div className="text-sm text-blue-600">
                                    <p>Format: minute heure jour_du_mois mois jour_de_la_semaine</p>
                                    <p>Exemples:</p>
                                    <ul className="list-disc list-inside">
                                        <li>* * * * * = Toutes les minutes</li>
                                        <li>0 * * * * = Toutes les heures</li>
                                        <li>0 0 * * * = Tous les jours à minuit</li>
                                        <li>0 0 * * 0 = Tous les dimanches à minuit</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Gestionnaire de fichiers */}
                    <div className="w-full max-w-5xl mt-6 bg-white shadow-md rounded-xl p-6">
                        <div className="flex justify-between items-center mb-6">
                            <div className="flex items-center gap-2 cursor-pointer" onClick={() => setIsFileManagerCollapsed(!isFileManagerCollapsed)}>
                                <h2 className="text-xl font-bold text-gray-800 flex items-center gap-2">
                                    <FaFolder className="text-blue-500" /> Gestionnaire de fichiers
                                </h2>
                                <button className="text-gray-500 hover:text-gray-700">
                                    {isFileManagerCollapsed ? '▼' : '▲'}
                                </button>
                            </div>
                            <div className="flex gap-2">
                                <button
                                    onClick={() => setShowApacheConfigModal(true)}
                                    className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center gap-1"
                                >
                                    <FaPlus size={12} /> Nouveau VirtualHost
                                </button>
                                <button
                                    onClick={() => setShowCreateModal(true)}
                                    className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center gap-1"
                                >
                                    <FaPlus size={12} /> Nouveau
                                </button>
                            </div>
                        </div>

                        <div className={`transition-all duration-300 ease-in-out overflow-hidden ${isFileManagerCollapsed ? 'max-h-0' : 'max-h-[2000px]'}`}>
                            {fileError && (
                                <div className="mb-4 text-red-500 text-sm">{fileError}</div>
                            )}

                            <div className="flex items-center gap-2 mb-4">
                                <button
                                    onClick={navigateUp}
                                    className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center gap-1"
                                >
                                    <FaArrowLeft size={12} /> Remonter
                                </button>
                                <span className="text-gray-600">{currentPath}</span>
                            </div>

                            <div className="grid grid-cols-1 gap-2">
                                {files.map((file) => (
                                    <div
                                        key={file.name}
                                        className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded-lg"
                                    >
                                        {file.type === 'directory' ? (
                                            <div className="cursor-pointer" onClick={() => handleFileClick(file)}>
                                                <FaFolder className="text-blue-500" />
                                            </div>
                                        ) : (
                                            <div className="flex items-center gap-2">
                                                <div className="cursor-pointer" onClick={() => handleFileClick(file)}>
                                                    <FaFile className="text-gray-500" />
                                                </div>
                                                <div className="flex gap-2">
                                                    <button
                                                        onClick={() => handleFileClick(file)}
                                                        className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                                                    >
                                                        Éditer en ligne
                                                    </button>
                                                    <button
                                                        onClick={() => handleDownloadFile(file)}
                                                        className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
                                                    >
                                                        Télécharger
                                                    </button>
                                                </div>
                                            </div>
                                        )}
                                        <div className="flex-1">
                                            <div className="font-medium">{file.name}</div>
                                            <div className="text-sm text-gray-500">
                                                {file.permissions} - {file.owner}:{file.group} - {file.size} - {file.date}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {selectedFile && (
                                <div className="mt-6">
                                    <div className="flex items-center justify-between mb-4">
                                        <h3 className="text-lg font-medium text-gray-700">{selectedFile}</h3>
                                        <div className="flex gap-2">
                                            {!isEditing ? (
                                                <button
                                                    onClick={() => setIsEditing(true)}
                                                    className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center gap-1"
                                                >
                                                    <FaEdit size={12} /> Éditer
                                                </button>
                                            ) : (
                                                <button
                                                    onClick={handleSaveFile}
                                                    className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center gap-1"
                                                >
                                                    <FaSave size={12} /> Sauvegarder
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                    <textarea
                                        value={fileContent}
                                        onChange={(e) => setFileContent(e.target.value)}
                                        className={`w-full h-64 font-mono text-sm p-4 rounded-lg border ${isEditing
                                            ? 'bg-white border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500'
                                            : 'bg-gray-50 border-gray-200'
                                            }`}
                                        readOnly={!isEditing}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                </>
            )}

            {/* Modal de création de configuration Apache */}
            {showApacheConfigModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-xl w-[600px]">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Créer une nouvelle configuration VirtualHost
                        </h3>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Nom de domaine</label>
                                <input
                                    type="text"
                                    value={apacheConfig.domain}
                                    onChange={(e) => setApacheConfig({ ...apacheConfig, domain: e.target.value })}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="exemple.com"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Nom d'utilisateur</label>
                                <input
                                    type="text"
                                    value={apacheConfig.username}
                                    onChange={(e) => setApacheConfig({ ...apacheConfig, username: e.target.value })}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="username"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">DocumentRoot personnalisé (optionnel)</label>
                                <input
                                    type="text"
                                    value={apacheConfig.customDocRoot}
                                    onChange={(e) => setApacheConfig({ ...apacheConfig, customDocRoot: e.target.value })}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="/home/<USER>/www/domain.com"
                                />
                                <p className="mt-1 text-sm text-gray-500">
                                    Laissez vide pour utiliser le chemin par défaut : /home/<USER>/www/domain.com
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Email de l'administrateur</label>
                                <input
                                    type="text"
                                    value={apacheConfig.serverAdmin}
                                    onChange={(e) => setApacheConfig({ ...apacheConfig, serverAdmin: e.target.value })}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="webmaster@localhost"
                                />
                            </div>
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="useSystemctl"
                                    checked={apacheConfig.useSystemctl}
                                    onChange={(e) => setApacheConfig({ ...apacheConfig, useSystemctl: e.target.checked })}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="useSystemctl" className="ml-2 block text-sm text-gray-900">
                                    Utiliser systemctl (décocher pour utiliser service)
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="enableSSL"
                                    checked={apacheConfig.enableSSL}
                                    onChange={(e) => setApacheConfig({ ...apacheConfig, enableSSL: e.target.checked })}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="enableSSL" className="ml-2 block text-sm text-gray-900">
                                    Activer HTTPS (SSL)
                                </label>
                            </div>
                            <div className="flex justify-end gap-2">
                                <button
                                    onClick={() => setShowApacheConfigModal(false)}
                                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                                >
                                    Annuler
                                </button>
                                <button
                                    onClick={handleCreateApacheConfig}
                                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                                >
                                    Créer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Modal de création */}
            {showCreateModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-xl w-96">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            Créer un nouveau {newItemType === 'file' ? 'fichier' : 'dossier'}
                        </h3>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Type</label>
                                <select
                                    value={newItemType}
                                    onChange={(e) => setNewItemType(e.target.value as 'file' | 'directory')}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                >
                                    <option value="file">Fichier</option>
                                    <option value="directory">Dossier</option>
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Nom</label>
                                <input
                                    type="text"
                                    value={newItemName}
                                    onChange={(e) => setNewItemName(e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    placeholder={newItemType === 'file' ? 'nom-du-fichier.txt' : 'nom-du-dossier'}
                                />
                            </div>
                            <div className="flex justify-end gap-2">
                                <button
                                    onClick={() => setShowCreateModal(false)}
                                    className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                                >
                                    Annuler
                                </button>
                                <button
                                    onClick={handleCreateItem}
                                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                                >
                                    Créer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Dashboard;