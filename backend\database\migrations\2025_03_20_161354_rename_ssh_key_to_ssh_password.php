<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::table('servers', function (Blueprint $table) {
            $table->renameColumn('ssh_key', 'ssh_password');
        });
    }

    public function down()
    {
        Schema::table('servers', function (Blueprint $table) {
            $table->renameColumn('ssh_password', 'ssh_key');
        });
    }
};
