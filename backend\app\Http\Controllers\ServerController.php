<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Server;
use phpseclib3\Net\SSH2;
use phpseclib3\Net\SFTP;
use Symfony\Component\Process\Process;

class ServerController extends Controller
{
    public function index()
    {
        return response()->json(Server::all());
    }

    public function status($id)
    {
        $server = Server::findOrFail($id);
    
        try {
            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                return response()->json(['status' => 'Connexion SSH échouée'], 500);
            }
    
            // CPU Usage
            $cpuUsage = $ssh->exec("top -bn1 | grep 'Cpu(s)' | awk '{print $2}'");
            
            // RAM Usage
            $ramUsage = $ssh->exec("free -m | awk 'NR==2{printf \"%.2f%%\", $3*100/$2 }'");
            
            // Disk Usage - Détecte automatiquement les disques
           /* $diskInfo = $ssh->exec("df -h | grep '^/dev/' | awk '{print $1\":\"$5}'");
            $disks = [];
            foreach (explode("\n", trim($diskInfo)) as $line) {
                if (!empty($line)) {
                    list($device, $usage) = explode(':', $line);
                    $disks[basename($device)] = trim($usage);
                }
            }*/
   $diskInfo = $ssh->exec(
  "df -P -h | awk 'NR>1 && $1!~/^(tmpfs|devtmpfs)$/{printf \"%s\\t%s\\t%s\\t%s\\n\", $1, $5, $4, $6}'"
);
// device \t use% \t avail \t mount

$disks = [];
foreach (explode("\n", trim($diskInfo)) as $line) {
    if ($line === '') continue;
    $parts = explode("\t", $line);
    if (count($parts) < 4) continue;
    list($device, $usage, $avail, $mount) = $parts;
    $key = basename($device) . " ($mount)";
    $disks[$key] = trim($usage) . " - " . trim($avail);
}


            // Informations système
            $systemInfo = [
               // 'os' => trim($ssh->exec("cat /etc/os-release 2>/dev/null | grep PRETTY_NAME | cut -d'\"' -f2 || cat /etc/issue | head -n1")),
                'os' => trim($ssh->exec("
                    if [ -f /etc/redhat-release ]; then 
                        /bin/cat /etc/redhat-release;
                    elif [ -f /etc/os-release ]; then 
                        grep '^PRETTY_NAME=' /etc/os-release | cut -d'\"' -f2;
                    else
                        head -n1 /etc/issue 2>/dev/null || echo 'N/A';
                    fi
                ")),
              // 'uptime' => trim($ssh->exec("uptime -p || uptime")),
                  'uptime' => trim($ssh->exec('
                    (uptime 2>/dev/null | grep -oP "up \K.*?(?=, [0-9]+ user)" || 
                    awk -F. "{print \$1}" /proc/uptime | xargs -I{} awk -v secs={} \
                    "BEGIN {
                        weeks = int(secs/604800);
                        days = int((secs%604800)/86400); 
                        hours = int((secs%86400)/3600); 
                        mins = int((secs%3600)/60); 
                        printf \"up %d weeks, %d days, %d hours, %d mins\", weeks, days, hours, mins
                    }" 
                    ) 2>/dev/null
                ')),  
              'load_average' => trim($ssh->exec("cat /proc/loadavg | awk '{print $1\",\"$2\",\"$3}'")),
                'processes' => [
                    'total' => trim($ssh->exec("ps aux 2>/dev/null | wc -l || ps -ef | wc -l")),
                    'running' => trim($ssh->exec("ps aux 2>/dev/null | grep R | wc -l || ps -ef | grep R | wc -l")),
                    'sleeping' => trim($ssh->exec("ps aux 2>/dev/null | grep S | wc -l || ps -ef | grep S | wc -l")),
                    'zombie' => trim($ssh->exec("ps aux 2>/dev/null | grep Z | wc -l || ps -ef | grep Z | wc -l"))
                ],
                'cpu_info' => [
                    'model' => trim($ssh->exec("cat /proc/cpuinfo 2>/dev/null | grep 'model name' | head -n1 | cut -d':' -f2 || echo 'N/A'")),
                    //'cores' => trim($ssh->exec("nproc || grep -c ^processor /proc/cpuinfo")),
                    'cores' => trim($ssh->exec("(/usr/bin/nproc 2>/dev/null || /bin/grep -c '^processor' /proc/cpuinfo 2>/dev/null || /bin/echo 1) | /usr/bin/head -n1")),
                    'threads' => trim($ssh->exec("cat /proc/cpuinfo 2>/dev/null | grep processor | wc -l || echo 'N/A'"))
                ]
            ];

            // Monitoring Réseau
            $networkInfo = [
                'interfaces' => [],
                'connections' => [
                    'total' => trim($ssh->exec("netstat -an 2>/dev/null | grep ESTABLISHED | wc -l || ss -t state established | wc -l")),
                    'listening' => trim($ssh->exec("netstat -an 2>/dev/null | grep LISTEN | wc -l || ss -t state listening | wc -l")),
                    'waiting' => trim($ssh->exec("netstat -an 2>/dev/null | grep TIME_WAIT | wc -l || ss -t state time-wait | wc -l"))
                ],
                'open_ports' => []
            ];

            // Récupération des interfaces réseau
            $interfaces = $ssh->exec("ip -o link show 2>/dev/null | awk -F': ' '{print $2}' | grep -v lo || ifconfig 2>/dev/null | grep '^[a-zA-Z]' | cut -d':' -f1");
            foreach (explode("\n", trim($interfaces)) as $interface) {
                if (!empty($interface)) {
                    $rx = trim($ssh->exec("cat /sys/class/net/$interface/statistics/rx_bytes 2>/dev/null || echo '0'"));
                    $tx = trim($ssh->exec("cat /sys/class/net/$interface/statistics/tx_bytes 2>/dev/null || echo '0'"));
                    $networkInfo['interfaces'][$interface] = [
                        'rx' => $rx,
                        'tx' => $tx
                    ];
                }
            }

            // Ports ouverts
            $ports = $ssh->exec("netstat -tuln 2>/dev/null | grep LISTEN | awk '{print $4}' | cut -d':' -f2 || ss -tuln | grep LISTEN | awk '{print $5}' | cut -d':' -f2");
            foreach (explode("\n", trim($ports)) as $port) {
                if (!empty($port)) {
                    $networkInfo['open_ports'][] = $port;
                }
            }
            
            return response()->json([
                'server' => $server,
                'cpu' => trim($cpuUsage),
                'ram' => trim($ramUsage),
                'disks' => $disks,
                'system' => $systemInfo,
                'network' => $networkInfo
            ]);
        } catch (\Exception $e) {
            return response()->json(['status' => 'Erreur: ' . $e->getMessage()], 500);
        }
    }

    public function executeCommand(Request $request, $id)
    {
        $server = Server::findOrFail($id);
        $command = $request->input('command');
        
        // Liste des commandes autorisées pour la sécurité
        $allowedCommands = [
            'apt-get' => ['update', 'upgrade', 'install', 'remove', 'list'],
            'yum' => ['update', 'upgrade', 'install', 'remove', 'list'],
            'systemctl' => ['status', 'start', 'stop', 'restart', 'enable', 'disable'],
            'df' => ['-h', '-i', '--total'],
            'free' => ['-m', '-h', '-t'],
            'top' => ['-bn1', '-n1'],
            'ps' => ['aux', '-ef', '-u'],
            'ls' => ['-la', '-l', '-a', '-R'],
            'cat' => ['*'],  // On permet cat pour tous les fichiers
            'grep' => ['*'], // On permet grep sans restriction
            'vim' => ['*'],
            'vi' => ['*'],
            'nano' => ['*'],
            'sudo' => ['*'],
            'TERM=xterm' => ['*'],
            'cd' => ['*'],
            'pwd' => ['*'],
            'whoami' => ['*'],
            'which' => ['*'],
            'test' => ['*'],
            'echo' => ['*'],
            'rm' => ['*'],
            'mkdir' => ['*'],
            'touch' => ['*'],
            'cp' => ['*'],
            'mv' => ['*'],
            'chmod' => ['*'],
            'chown' => ['*']
        ];

        // Vérification si c'est une commande d'édition
        $isEditor = false;
        foreach (['vim', 'vi', 'nano'] as $editor) {
            if (strpos($command, $editor) !== false) {
                $isEditor = true;
                break;
            }
        }

        // Si ce n'est pas un éditeur, vérifier la sécurité
        if (!$isEditor) {
            $commandParts = explode(' ', trim($command));
            $baseCommand = $commandParts[0];
            
            // Support pour les commandes avec sudo
            if ($baseCommand === 'sudo' && count($commandParts) > 1) {
                $baseCommand = $commandParts[1];
            }
            
            if (!array_key_exists($baseCommand, $allowedCommands)) {
                return response()->json([
                    'error' => 'Commande non autorisée: ' . $baseCommand,
                    'allowed_commands' => array_keys($allowedCommands)
                ], 403);
            }
        }

        try {
            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                return response()->json(['error' => 'Connexion SSH échouée'], 500);
            }

            if ($isEditor) {
                // Pour les éditeurs, on utilise un pseudo-terminal
                $ssh->enablePTY();
                $ssh->setTimeout(1);
                try {
                    $output = $ssh->exec($command);
                } catch (\Exception $e) {
                    // C'est normal d'avoir une exception ici car l'éditeur est interactif
                    $output = "Éditeur ouvert. Utilisez les commandes de l'éditeur pour modifier le fichier.";
                }
            } else {
                // Pour les autres commandes, exécution normale
                $output = $ssh->exec($command . ' 2>&1');
            }
            
            // Log de la commande pour l'audit
            \Log::info("Commande exécutée sur {$server->name}: $command");

            return response()->json([
                'success' => true,
                'output' => $output
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function getCrons($id)
    {
        $server = Server::findOrFail($id);
    
        try {
            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                return response()->json(['error' => 'Connexion SSH échouée'], 500);
            }

            // Récupérer les crons de l'utilisateur courant
            $userCrons = $ssh->exec("crontab -l 2>/dev/null || echo 'Aucun cron configuré'");
            
            // Récupérer les crons système
            $systemCrons = $ssh->exec("for file in /etc/cron.d/* /etc/crontab; do if [ -f \$file ]; then echo \"=== \$file ===\"; cat \$file 2>/dev/null; echo; fi; done");

            return response()->json([
                'user_crons' => $userCrons,
                'system_crons' => $systemCrons
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function addCron(Request $request, $id)
    {
        $server = Server::findOrFail($id);
        $schedule = $request->input('schedule');
        $command = $request->input('command');

        // Validation basique du format cron
        if (!preg_match('/^(\*|[0-9,\-\/]+)\s+(\*|[0-9,\-\/]+)\s+(\*|[0-9,\-\/]+)\s+(\*|[0-9,\-\/]+)\s+(\*|[0-9,\-\/]+)/', $schedule)) {
            return response()->json(['error' => 'Format de planification invalide'], 400);
        }

        try {
            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                return response()->json(['error' => 'Connexion SSH échouée'], 500);
            }

            // Récupérer les crons existants
            $currentCrons = $ssh->exec("crontab -l 2>/dev/null || echo ''");
            
            // Ajouter le nouveau cron
            $newCron = "$schedule $command";
            $allCrons = trim($currentCrons) . "\n" . $newCron;
            
            // Créer un fichier temporaire avec les crons
            $tempFile = tempnam(sys_get_temp_dir(), 'cron');
            file_put_contents($tempFile, $allCrons);
            
            // Uploader et installer le nouveau crontab
            $ssh->put('/tmp/newcron', file_get_contents($tempFile));
            $ssh->exec("crontab /tmp/newcron");
            $ssh->exec("rm /tmp/newcron");
            
            unlink($tempFile);

            return response()->json([
                'success' => true,
                'message' => 'Cron ajouté avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function deleteCron(Request $request, $id)
    {
        $server = Server::findOrFail($id);
        $cronLine = $request->input('cron_line');

        try {
            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                return response()->json(['error' => 'Connexion SSH échouée'], 500);
            }

            // Récupérer les crons existants
            $currentCrons = $ssh->exec("crontab -l");
            
            // Supprimer la ligne spécifique
            $crons = explode("\n", $currentCrons);
            $crons = array_filter($crons, function($line) use ($cronLine) {
                return trim($line) !== trim($cronLine);
            });
            
            // Créer un fichier temporaire avec les crons restants
            $tempFile = tempnam(sys_get_temp_dir(), 'cron');
            file_put_contents($tempFile, implode("\n", $crons));
            
            // Uploader et installer le nouveau crontab
            $ssh->put('/tmp/newcron', file_get_contents($tempFile));
            $ssh->exec("crontab /tmp/newcron");
            $ssh->exec("rm /tmp/newcron");
            
            unlink($tempFile);

            return response()->json([
                'success' => true,
                'message' => 'Cron supprimé avec succès'
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function readFile(Request $request, $id)
    {
        $server = Server::findOrFail($id);
        $filePath = $request->input('path');

        // Vérification de sécurité basique pour éviter la traversée de répertoire
        if (strpos($filePath, '..') !== false) {
            return response()->json(['error' => 'Chemin non autorisé'], 403);
        }

        try {
            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                return response()->json(['error' => 'Connexion SSH échouée'], 500);
            }

            // Vérifier si le fichier existe
            $fileExists = $ssh->exec("test -f " . escapeshellarg($filePath) . " && echo 'exists'");
            if (trim($fileExists) !== 'exists') {
                return response()->json(['error' => 'Fichier non trouvé'], 404);
            }

            // Vérifier les permissions
            $permissions = $ssh->exec("ls -l " . escapeshellarg($filePath) . " | cut -d ' ' -f1");
            if (strpos($permissions, 'r') === false) {
                return response()->json(['error' => 'Permission de lecture refusée'], 403);
            }

            // Lire le contenu du fichier
            $content = $ssh->exec("cat " . escapeshellarg($filePath));

            return response()->json([
                'success' => true,
                'content' => $content,
                'path' => $filePath
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function writeFile(Request $request, $id)
    {
        try {
            // Log des paramètres reçus
            \Log::info('writeFile - Paramètres reçus:', [
                'id' => $id,
                'file_path' => $request->input('file_path'),
                'content_length' => strlen($request->input('content')),
                'all_params' => $request->all()
            ]);

            $server = Server::findOrFail($id);
            $filePath = $request->input('file_path');
            $content = $request->input('content');

            // Log après récupération des données
            \Log::info('writeFile - Données validées:', [
                'server_id' => $server->id,
                'file_path' => $filePath,
                'content_length' => strlen($content)
            ]);

            // Log de la validation du chemin
            \Log::info('writeFile - Validation du chemin:', [
                'file_path' => $filePath,
                'is_valid' => $this->isValidPath($filePath)
            ]);

            // Vérifier que le chemin est valide et sécurisé
            if (!$this->isValidPath($filePath)) {
                \Log::error('writeFile - Chemin non valide:', ['file_path' => $filePath]);
                return response()->json(['error' => 'Chemin de fichier non valide'], 400);
            }

            // Initialiser les connexions SSH et SFTP
            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                \Log::error('writeFile - Échec de la connexion SSH:', [
                    'server_ip' => $server->ip,
                    'ssh_user' => $server->ssh_user
                ]);
                return response()->json(['error' => 'Connexion SSH échouée'], 500);
            }

            $sftp = new SFTP($server->ip);
            if (!$sftp->login($server->ssh_user, $server->ssh_password)) {
                \Log::error('writeFile - Échec de la connexion SFTP');
                return response()->json(['error' => 'Connexion SFTP échouée'], 500);
            }

            \Log::info('writeFile - Connexions SSH et SFTP établies');

            // Vérifier l'utilisateur courant avant sudo
            $currentUser = trim($ssh->exec('whoami'));
            \Log::info('writeFile - Utilisateur courant:', ['user' => $currentUser]);

            // Vérifier si sudo nécessite un mot de passe
            $sudoTest = $ssh->exec('sudo -n true 2>&1');
            $requiresPassword = strpos($sudoTest, 'password') !== false;
            \Log::info('writeFile - Test sudo:', [
                'requires_password' => $requiresPassword,
                'sudo_test_output' => $sudoTest
            ]);

            // Vérifier si le répertoire parent existe
            $dirPath = dirname($filePath);
            $dirCheck = $ssh->exec("test -d " . escapeshellarg($dirPath) . " && echo 'exists'");
            \Log::info('writeFile - Vérification du répertoire parent:', [
                'dir_path' => $dirPath,
                'exists' => trim($dirCheck) === 'exists'
            ]);

            if (trim($dirCheck) !== 'exists') {
                \Log::error('writeFile - Répertoire parent inexistant:', ['dir_path' => $dirPath]);
                return response()->json(['error' => 'Le répertoire parent n\'existe pas'], 400);
            }

            // Créer un fichier temporaire avec un nom unique
            $tempFile = '/tmp/edit_' . uniqid();
            \Log::info('writeFile - Fichier temporaire créé:', ['temp_file' => $tempFile]);

            // Uploader directement le contenu via SFTP
            if (!$sftp->put($tempFile, $content)) {
                \Log::error('writeFile - Échec de l\'upload du fichier temporaire');
                return response()->json([
                    'error' => 'Erreur lors de l\'upload du fichier temporaire'
                ], 500);
            }

            \Log::info('writeFile - Fichier temporaire uploadé sur le serveur');

            // S'assurer que le fichier temporaire sur le serveur a les bonnes permissions
            $ssh->exec('chmod 644 ' . escapeshellarg($tempFile));

            // Construire et exécuter la commande de copie
            $copyCmd = $requiresPassword
                ? sprintf(
                    'echo %s | sudo -S bash -c "cp -p %s %s && chown --reference=%s %s && chmod --reference=%s %s 2>&1"',
                    escapeshellarg($server->ssh_password),
                    escapeshellarg($tempFile),
                    escapeshellarg($filePath),
                    escapeshellarg(dirname($filePath)),
                    escapeshellarg($filePath),
                    escapeshellarg(dirname($filePath)),
                    escapeshellarg($filePath)
                )
                : sprintf(
                    'sudo bash -c "cp -p %s %s && chown --reference=%s %s && chmod --reference=%s %s 2>&1"',
                    escapeshellarg($tempFile),
                    escapeshellarg($filePath),
                    escapeshellarg(dirname($filePath)),
                    escapeshellarg($filePath),
                    escapeshellarg(dirname($filePath)),
                    escapeshellarg($filePath)
                );

            \Log::info('writeFile - Commande de copie construite:', ['requires_password' => $requiresPassword]);

            $copyResult = $ssh->exec($copyCmd);
            \Log::info('writeFile - Résultat de la copie:', ['result' => $copyResult]);

            // Nettoyer le fichier temporaire
            $ssh->exec('rm -f ' . escapeshellarg($tempFile));

            // Vérifier si le fichier existe après la copie
            $fileCheck = $ssh->exec("test -f " . escapeshellarg($filePath) . " && echo 'success'");
            \Log::info('writeFile - Vérification finale du fichier:', [
                'exists' => trim($fileCheck) === 'success'
            ]);

            if (trim($fileCheck) !== 'success') {
                \Log::error('writeFile - Fichier non trouvé après la copie:', [
                    'file_path' => $filePath,
                    'copy_result' => $copyResult
                ]);
                return response()->json([
                    'error' => 'Erreur lors de la copie du fichier',
                    'details' => $copyResult
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Fichier sauvegardé avec succès'
            ]);

        } catch (\Exception $e) {
            \Log::error('writeFile - Exception:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => 'Erreur lors de la sauvegarde du fichier',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    private function isValidPath($path)
    {
        // Vérifier que le chemin ne contient pas de séquences dangereuses
        if (strpos($path, '..') !== false) {
            return false;
        }

        // Vérifier que le chemin commence par /
        if (strpos($path, '/') !== 0) {
            return false;
        }

        return true;
    }

    public function listFiles(Request $request, $id)
    {
        $server = Server::findOrFail($id);
        $path = $request->input('path', '/');

        // Vérification de sécurité basique
        if (strpos($path, '..') !== false) {
            return response()->json(['error' => 'Chemin non autorisé'], 403);
        }

        try {
            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                return response()->json(['error' => 'Connexion SSH échouée'], 500);
            }

            // Lister les fichiers avec leurs détails
            $files = $ssh->exec("ls -la " . escapeshellarg($path) . " | awk '{print \$1,\$3,\$4,\$5,\$6,\$7,\$8,\$9}'");
            
            // Parser la sortie en tableau structuré
            $fileList = [];
            foreach (explode("\n", trim($files)) as $line) {
                if (empty($line) || $line === 'total' || strpos($line, 'total') === 0) continue;
                
                $parts = preg_split('/\s+/', $line);
                if (count($parts) >= 8) {
                    $fileList[] = [
                        'permissions' => $parts[0],
                        'owner' => $parts[1],
                        'group' => $parts[2],
                        'size' => $parts[3],
                        'date' => implode(' ', array_slice($parts, 4, 3)),
                        'name' => $parts[7],
                        'type' => $parts[0][0] === 'd' ? 'directory' : 'file'
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'path' => $path,
                'files' => $fileList
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function openEditor(Request $request, $id)
    {
        $server = Server::findOrFail($id);
        $filePath = $request->input('path');
        $editor = $request->input('editor', 'nano');

        if (strpos($filePath, '..') !== false) {
            return response()->json(['error' => 'Chemin non autorisé'], 403);
        }

        if (!in_array($editor, ['nano', 'vi'])) {
            return response()->json(['error' => 'Éditeur non autorisé'], 403);
        }

        try {
            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                return response()->json(['error' => 'Connexion SSH échouée'], 500);
            }

            // Vérifier si l'éditeur est installé
            $editorExists = $ssh->exec("which $editor");
            if (empty($editorExists)) {
                return response()->json(['error' => "L'éditeur $editor n'est pas installé sur le serveur"], 404);
            }

            // Vérifier si sudo nécessite un mot de passe
            $sudoTest = $ssh->exec('sudo -n true 2>&1');
            $requiresPassword = strpos($sudoTest, 'password') !== false;

            // Préparer la commande de base
            $baseCommand = $editor === 'vi' ? 'vim' : $editor;

            // Vérifier les permissions du fichier
            $fileCheck = $ssh->exec("test -f " . escapeshellarg($filePath) . " && echo 'exists' || echo 'not found'");
            $needsSudo = false;

            if (trim($fileCheck) === 'exists') {
                $filePerms = $ssh->exec("test -w " . escapeshellarg($filePath) . " && echo 'writable' || echo 'not writable'");
                $needsSudo = trim($filePerms) !== 'writable';
            } else {
                $dirPath = dirname($filePath);
                $dirWritable = $ssh->exec("test -w " . escapeshellarg($dirPath) . " && echo 'writable' || echo 'not writable'");
                $needsSudo = trim($dirWritable) !== 'writable';
            }

            if ($needsSudo) {
                if ($requiresPassword) {
                    // Si sudo nécessite un mot de passe
                    $command = sprintf(
                        "echo %s | sudo -S %s %s",
                        escapeshellarg($server->ssh_password),
                        escapeshellarg($baseCommand),
                        escapeshellarg($filePath)
                    );
                } else {
                    // Si sudo ne nécessite pas de mot de passe
                    $command = sprintf(
                        "sudo %s %s",
                        escapeshellarg($baseCommand),
                        escapeshellarg($filePath)
                    );
                }

                return response()->json([
                    'success' => true,
                    'message' => "Commande d'édition avec privilèges root",
                    'command' => $command,
                    'requires_sudo' => true,
                    'requires_password' => $requiresPassword,
                    'editor' => $editor
                ]);
            }

            // Si on n'a pas besoin de sudo
            $command = sprintf(
                "%s %s",
                escapeshellarg($baseCommand),
                escapeshellarg($filePath)
            );

            return response()->json([
                'success' => true,
                'message' => "Commande d'édition prête",
                'command' => $command,
                'requires_sudo' => false,
                'editor' => $editor
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function createFile(Request $request, $id)
    {
        try {
            $server = Server::findOrFail($id);
            $filePath = $request->input('file_path');

            // Log des paramètres reçus
            \Log::info('createFile - Paramètres reçus:', [
                'id' => $id,
                'file_path' => $filePath
            ]);

            // Vérifier que le chemin est valide et sécurisé
            if (!$this->isValidPath($filePath)) {
                \Log::error('createFile - Chemin non valide:', ['file_path' => $filePath]);
                return response()->json(['error' => 'Chemin de fichier non valide'], 400);
            }

            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                \Log::error('createFile - Échec de la connexion SSH');
                return response()->json(['error' => 'Connexion SSH échouée'], 500);
            }

            // Vérifier si le fichier existe déjà
            $fileExists = $ssh->exec("test -e " . escapeshellarg($filePath) . " && echo 'exists'");
            if (trim($fileExists) === 'exists') {
                return response()->json(['error' => 'Le fichier existe déjà'], 400);
            }

            // Vérifier si sudo nécessite un mot de passe
            $sudoTest = $ssh->exec('sudo -n true 2>&1');
            $requiresPassword = strpos($sudoTest, 'password') !== false;

            // Créer le fichier avec les bonnes permissions
            $createCmd = $requiresPassword
                ? sprintf(
                    'echo %s | sudo -S touch %s && sudo chmod 644 %s && sudo chown %s:%s %s',
                    escapeshellarg($server->ssh_password),
                    escapeshellarg($filePath),
                    escapeshellarg($filePath),
                    escapeshellarg($server->ssh_user),
                    escapeshellarg($server->ssh_user),
                    escapeshellarg($filePath)
                )
                : sprintf(
                    'sudo touch %s && sudo chmod 644 %s && sudo chown %s:%s %s',
                    escapeshellarg($filePath),
                    escapeshellarg($filePath),
                    escapeshellarg($server->ssh_user),
                    escapeshellarg($server->ssh_user),
                    escapeshellarg($filePath)
                );

            $result = $ssh->exec($createCmd);

            // Vérifier si le fichier a été créé
            $fileCheck = $ssh->exec("test -f " . escapeshellarg($filePath) . " && echo 'success'");
            if (trim($fileCheck) !== 'success') {
                \Log::error('createFile - Échec de la création du fichier:', ['result' => $result]);
                return response()->json([
                    'error' => 'Erreur lors de la création du fichier',
                    'details' => $result
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Fichier créé avec succès'
            ]);

        } catch (\Exception $e) {
            \Log::error('createFile - Exception:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => 'Erreur lors de la création du fichier',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    public function createDirectory(Request $request, $id)
    {
        try {
            $server = Server::findOrFail($id);
            $dirPath = $request->input('directory_path');

            // Log des paramètres reçus
            \Log::info('createDirectory - Paramètres reçus:', [
                'id' => $id,
                'directory_path' => $dirPath
            ]);

            // Vérifier que le chemin est valide et sécurisé
            if (!$this->isValidPath($dirPath)) {
                \Log::error('createDirectory - Chemin non valide:', ['directory_path' => $dirPath]);
                return response()->json(['error' => 'Chemin de répertoire non valide'], 400);
            }

            $ssh = new SSH2($server->ip);
            if (!$ssh->login($server->ssh_user, $server->ssh_password)) {
                \Log::error('createDirectory - Échec de la connexion SSH');
                return response()->json(['error' => 'Connexion SSH échouée'], 500);
            }

            // Vérifier si le répertoire existe déjà
            $dirExists = $ssh->exec("test -e " . escapeshellarg($dirPath) . " && echo 'exists'");
            if (trim($dirExists) === 'exists') {
                return response()->json(['error' => 'Le répertoire existe déjà'], 400);
            }

            // Vérifier si sudo nécessite un mot de passe
            $sudoTest = $ssh->exec('sudo -n true 2>&1');
            $requiresPassword = strpos($sudoTest, 'password') !== false;

            // Créer le répertoire avec les bonnes permissions
            $createCmd = $requiresPassword
                ? sprintf(
                    'echo %s | sudo -S mkdir -p %s && sudo chmod 755 %s && sudo chown %s:%s %s',
                    escapeshellarg($server->ssh_password),
                    escapeshellarg($dirPath),
                    escapeshellarg($dirPath),
                    escapeshellarg($server->ssh_user),
                    escapeshellarg($server->ssh_user),
                    escapeshellarg($dirPath)
                )
                : sprintf(
                    'sudo mkdir -p %s && sudo chmod 755 %s && sudo chown %s:%s %s',
                    escapeshellarg($dirPath),
                    escapeshellarg($dirPath),
                    escapeshellarg($server->ssh_user),
                    escapeshellarg($server->ssh_user),
                    escapeshellarg($dirPath)
                );

            $result = $ssh->exec($createCmd);

            // Vérifier si le répertoire a été créé
            $dirCheck = $ssh->exec("test -d " . escapeshellarg($dirPath) . " && echo 'success'");
            if (trim($dirCheck) !== 'success') {
                \Log::error('createDirectory - Échec de la création du répertoire:', ['result' => $result]);
                return response()->json([
                    'error' => 'Erreur lors de la création du répertoire',
                    'details' => $result
                ], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Répertoire créé avec succès'
            ]);

        } catch (\Exception $e) {
            \Log::error('createDirectory - Exception:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'error' => 'Erreur lors de la création du répertoire',
                'details' => $e->getMessage()
            ], 500);
        }
    }
}
